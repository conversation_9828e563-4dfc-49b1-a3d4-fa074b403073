package dto

import "net/http"

// LLMConfig holds configuration for LLM client
type LLMConfig struct {
	APIKey      string
	Model       string
	MaxTokens   int
	Temperature float32
	Timeout     int // seconds
}

// GeminiClient implements LLMClient using Google's Gemini API
type GeminiClient struct {
	config     LLMConfig
	httpClient *http.Client
	baseURL    string
}

type APIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}