package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/RemedyMate/remedymate-backend/domain/dto"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
)

// GeminiClient implements LLMClient using Google's Gemini API
type GeminiClient struct {
	config     dto.LLMConfig
	httpClient *http.Client
	baseURL    string
}

// NewGeminiClient creates a new Gemini client
func NewGeminiClient(config dto.LLMConfig) interfaces.LLMClient {
	return &GeminiClient{
		config:     config,
		httpClient: &http.Client{Timeout: time.Duration(config.Timeout) * time.Second},
		baseURL:    "https://generativelanguage.googleapis.com/v1beta/models",
	}
}

// Gemini-specific request/response structures
type GeminiRequest struct {
	Contents         []GeminiContent       `json:"contents"`
	GenerationConfig GeminiGenConfig       `json:"generationConfig"`
	SafetySettings   []GeminiSafetySetting `json:"safetySettings,omitempty"`
}

type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
	Role  string       `json:"role,omitempty"`
}

type GeminiPart struct {
	Text string `json:"text"`
}

type GeminiGenConfig struct {
	Temperature     float32 `json:"temperature"`
	MaxOutputTokens int     `json:"maxOutputTokens"`
	TopP            float32 `json:"topP"`
}

type GeminiSafetySetting struct {
	Category  string `json:"category"`
	Threshold string `json:"threshold"`
}

type GeminiResponse struct {
	Candidates []GeminiCandidate `json:"candidates"`
	Error      *dto.APIError         `json:"error,omitempty"`
}

type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
}

// ClassifyTriage calls Gemini API for triage classification
func (g *GeminiClient) ClassifyTriage(ctx context.Context, prompt string) (string, error) {
	return g.callGemini(ctx, prompt)
}

// MapTopic calls Gemini API for topic mapping
func (g *GeminiClient) MapTopic(ctx context.Context, prompt string) (string, error) {
	return g.callGemini(ctx, prompt)
}

// ComposeGuidance calls Gemini API for guidance composition
func (g *GeminiClient) ComposeGuidance(ctx context.Context, prompt string) (string, error) {
	return g.callGemini(ctx, prompt)
}

// callGemini makes the actual API call to Gemini
func (g *GeminiClient) callGemini(ctx context.Context, prompt string) (string, error) {
	// 1. Construct the request for Gemini's API format
	geminiReq := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{{Text: prompt}},
				Role:  "user",
			},
		},
		GenerationConfig: GeminiGenConfig{
			Temperature:     g.config.Temperature,
			MaxOutputTokens: g.config.MaxTokens,
			TopP:            0.95,
		},
		SafetySettings: []GeminiSafetySetting{
			{Category: "HARM_CATEGORY_DANGEROUS_CONTENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
			{Category: "HARM_CATEGORY_MEDICAL", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
		},
	}

	jsonData, err := json.Marshal(geminiReq)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// 2. Build the HTTP request
	modelURL := fmt.Sprintf("%s/%s:generateContent?key=%s", g.baseURL, g.config.Model, g.config.APIKey)
	req, err := http.NewRequestWithContext(ctx, "POST", modelURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 3. Execute the request
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("API request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	// 4. Parse the Gemini response
	var geminiResp GeminiResponse
	if err := json.Unmarshal(body, &geminiResp); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if geminiResp.Error != nil {
		return "", fmt.Errorf("gemini API error: %s", geminiResp.Error.Message)
	}

	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no response content returned from Gemini")
	}

// 5. Return the generated text
	return geminiResp.Candidates[0].Content.Parts[0].Text, nil
}

// ExtractKeywords calls Gemini API for keyword extraction
func (g *GeminiClient) ExtractKeywords(ctx context.Context, text string) ([]string, error) {
	systemPrompt := `Extract the main medical keywords from the user's text.
Your ONLY output must be a JSON array of strings: ["keyword1", "keyword2", "keyword3"]
Focus on symptoms, body parts, and medical terms. Maximum 5 keywords.`

	fullPrompt := fmt.Sprintf("%s\n\nUser text: %s", systemPrompt, text)
	response, err := g.callGemini(ctx, fullPrompt)
	if err != nil {
		return nil, err
	}

	var keywords []string
	if err := json.Unmarshal([]byte(response), &keywords); err != nil {
		return nil, fmt.Errorf("failed to parse keywords response: %w", err)
	}

	return keywords, nil
}